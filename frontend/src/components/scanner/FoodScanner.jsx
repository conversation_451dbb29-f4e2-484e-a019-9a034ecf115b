import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import Modal from '../ui/Modal';
import { useIsMobile } from '../../hooks/useIsMobile';
import { useGlobalLoading } from '../../hooks/useGlobalLoading';
import { useApiConfig } from '../../context/ApiConfigContext';
import ScannerButton from './ScannerButton';
import ScannerViewfinder, { getViewfinderConfig } from './ScannerViewfinder';
import { applyMediaDevicesPolyfill } from '../../utils/mediaDevicesPolyfill';
import foodDetector from '../../services/FoodDetector';
import ollamaProgressService from '../../services/ollamaProgressService';

/**
 * ComingSoonOverlay component
 * Displays a translucent "Coming Soon" banner with Apple Vision OS inspired design
 *
 * @param {string} feature - The name of the feature that's coming soon
 * @param {function} onClose - Function to call when the overlay is closed
 */
const ComingSoonOverlay = ({ feature, onClose }) => {
  return (
    <div className="absolute inset-0 flex items-center justify-center z-30 coming-soon-overlay">
      {/* Backdrop blur overlay */}
      <div className="absolute inset-0 backdrop-blur-md bg-black bg-opacity-30" onClick={onClose}></div>

      {/* Main banner container */}
      <div className="relative coming-soon-banner">
        {/* Frosted glass background with gradient */}
        <div className="absolute inset-0 rounded-2xl backdrop-blur-xl bg-gradient-to-br from-white/20 via-gray-100/10 to-black/20 border border-white/30 shadow-2xl"></div>

        {/* Content */}
        <div className="relative p-8 text-center">
          {/* Icon */}
          <div className="mb-6">
            <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-br from-white/30 to-gray-400/20 backdrop-blur-sm border border-white/40 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>

          {/* Title */}
          <h3 className="text-2xl font-semibold mb-3 text-white tracking-tight">
            {feature}
          </h3>
          <p className="text-lg font-medium mb-4 text-white/90">
            Coming Soon
          </p>

          {/* Description */}
          <p className="mb-8 text-white/70 text-sm leading-relaxed max-w-xs mx-auto">
            We're working hard to bring you this feature in a future update. Stay tuned!
          </p>

          {/* Close button */}
          <button
            onClick={onClose}
            className="coming-soon-button"
          >
            <span className="relative z-10 font-medium">Got it</span>
          </button>
        </div>
      </div>
    </div>
  );
};

/**
 * FoodScanner component for scanning food items
 *
 * @param {boolean} isOpen - Whether the scanner is open
 * @param {function} onClose - Function to call when the scanner is closed
 */
const FoodScanner = ({ isOpen, onClose }) => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const globalLoading = useGlobalLoading();
  const { getApiConfig, isConfigValid } = useApiConfig();
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const [stream, setStream] = useState(null);
  const [activeMode, setActiveMode] = useState('scan'); // 'scan', 'barcode', 'label', 'nutrition'
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [hasCamera, setHasCamera] = useState(false);
  const [capturedImage, setCapturedImage] = useState(null); // Store the captured image URL
  const [isFoodDetected, setIsFoodDetected] = useState(false); // Whether food is detected in the frame (immediate)
  const [isFoodDetectedStable, setIsFoodDetectedStable] = useState(false); // Whether food is detected (debounced for 3 seconds)
  const [foodDetectionActive, setFoodDetectionActive] = useState(false); // Whether food detection is active
  const [currentPrediction, setCurrentPrediction] = useState(null); // Current prediction from the model
  const [modelLoading, setModelLoading] = useState(false); // Whether the model is loading
  const detectionIntervalRef = useRef(null); // Reference to the detection interval
  const foodDetectionDebounceTimer = useRef(null); // Reference to the food detection debounce timer
  const [showComingSoon, setShowComingSoon] = useState(false); // Whether to show the "Coming Soon" overlay
  const [comingSoonFeature, setComingSoonFeature] = useState(''); // The feature that's coming soon

  // Apply MediaDevices polyfill when component mounts
  useEffect(() => {
    if (!isMobile) return;

    // Apply polyfill for MediaDevices API
    try {
      applyMediaDevicesPolyfill();
    } catch (err) {
      console.error('Error applying MediaDevices polyfill:', err);
    }

    // We'll assume camera is available on mobile devices
    // and only check when user tries to use it
    setHasCamera(true);
  }, [isMobile]);

  // Add/remove scanner-page class to <body> for notch overlay
  useEffect(() => {
    if (isOpen) {
      document.body.classList.add('scanner-page');
    } else {
      document.body.classList.remove('scanner-page');
    }
    return () => {
      document.body.classList.remove('scanner-page');
    };
  }, [isOpen]);

  // Load TensorFlow.js model when component mounts
  useEffect(() => {
    let modelLoaded = false;

    if (isOpen) {
      const loadModel = async () => {
        try {
          setModelLoading(true);
          await foodDetector.loadModel();
          setModelLoading(false);
          modelLoaded = true;
          console.log('TensorFlow.js model loaded successfully');

          // Check if food detection is unavailable
          if (window.foodDetectionUnavailable) {
            console.warn('Food detection is unavailable. Allowing capture without food detection.');
            // If food detection is unavailable, we'll allow capture in scan mode
            // by setting both immediate and stable food detection to true for all modes
            setIsFoodDetected(true);
            setIsFoodDetectedStable(true);
          }
        } catch (err) {
          console.error('Error loading TensorFlow.js model:', err);
          setModelLoading(false);
          globalLoading.stop.food();

          // If there's an error loading the model, allow capture without food detection
          window.foodDetectionUnavailable = true;
          setIsFoodDetected(true);
        }
      };

      loadModel();
    }

    // Cleanup function that runs when component unmounts or dependencies change
    return () => {
      // Clean up detection interval
      if (detectionIntervalRef.current) {
        clearInterval(detectionIntervalRef.current);
        detectionIntervalRef.current = null;
      }

      // Clean up food detection debounce timer
      if (foodDetectionDebounceTimer.current) {
        clearTimeout(foodDetectionDebounceTimer.current);
        foodDetectionDebounceTimer.current = null;
      }

      // Clean up TensorFlow.js model resources if we loaded it
      if (modelLoaded) {
        console.log('Cleaning up TensorFlow.js model resources');
        // Call dispose without try/catch since the method now has its own error handling
        foodDetector.dispose();
      }
    };
  }, [isOpen]);

  // Reset scanner state when modal opens for a fresh scan
  useEffect(() => {
    if (isOpen) {
      console.log('Scanner opened, resetting state for fresh scan');
      // Reset all scanner state to ensure a fresh start
      setCapturedImage(null);
      setLoading(false);
      setError(null);
      setIsFoodDetected(false);
      setIsFoodDetectedStable(false);
      setFoodDetectionActive(false);
      setCurrentPrediction(null);
      setActiveMode('scan');
      setShowComingSoon(false);
      setComingSoonFeature('');

      // Clear any existing debounce timer
      if (foodDetectionDebounceTimer.current) {
        clearTimeout(foodDetectionDebounceTimer.current);
        foodDetectionDebounceTimer.current = null;
      }
    }
  }, [isOpen]);

  // Start camera when modal opens
  useEffect(() => {
    if (isOpen && hasCamera) {
      // Short delay before starting camera to ensure UI is ready
      setTimeout(() => {
        startCamera();
      }, 300);

      // Add direct notch area styling for iOS devices
      if (/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream) {
        // Create a direct notch cover element
        let directNotchCover = document.getElementById('direct-notch-cover');
        if (!directNotchCover) {
          directNotchCover = document.createElement('div');
          directNotchCover.id = 'direct-notch-cover';
          directNotchCover.style.cssText = `
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            width: 100% !important;
            height: env(safe-area-inset-top, 0) !important;
            background-color: rgba(0, 0, 0, 0.25) !important;
            z-index: 999999 !important;
            pointer-events: none !important;
            opacity: 1 !important;
          `;
          document.body.appendChild(directNotchCover);
        }

        // Add a direct style tag for the notch area
        let directStyleTag = document.getElementById('direct-notch-style');
        if (!directStyleTag) {
          directStyleTag = document.createElement('style');
          directStyleTag.id = 'direct-notch-style';
          directStyleTag.textContent = `
            @supports (-webkit-touch-callout: none) {
              body::before {
                display: none !important;
              }

              #direct-notch-cover {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                width: 100% !important;
                height: env(safe-area-inset-top, 0) !important;
                background-color: rgba(0, 0, 0, 0.25) !important;
                z-index: 999999 !important;
                pointer-events: none !important;
                opacity: 1 !important;
              }
            }
          `;
          document.head.appendChild(directStyleTag);
        }
      }
    }

    return () => {
      // Ensure camera is stopped when component unmounts or modal closes
      stopCamera();

      // Remove direct notch cover
      const directNotchCover = document.getElementById('direct-notch-cover');
      if (directNotchCover && document.body.contains(directNotchCover)) {
        document.body.removeChild(directNotchCover);
      }

      // Remove direct style tag
      const directStyleTag = document.getElementById('direct-notch-style');
      if (directStyleTag && document.head.contains(directStyleTag)) {
        document.head.removeChild(directStyleTag);
      }
    };
  }, [isOpen, hasCamera]);

  // Handle page visibility changes to properly manage camera resources
  useEffect(() => {
    // Only set up visibility listeners when the scanner is open
    if (!isOpen) return;

    // Function to handle visibility change
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Page is hidden (user switched tabs or minimized browser)
        console.log('Page hidden, stopping camera to save resources');
        stopCamera();
      } else if (isOpen && hasCamera) {
        // Page is visible again and scanner should be open
        console.log('Page visible again, restarting camera');
        startCamera();
      }
    };

    // Add event listener for visibility change
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Clean up event listener when component unmounts or modal closes
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isOpen, hasCamera]);

  // Final cleanup when component unmounts completely
  useEffect(() => {
    // This cleanup function will run when the component unmounts completely

    // Add a beforeunload event listener to clean up resources when the user navigates away
    const handleBeforeUnload = () => {
      console.log('Page unloading, performing emergency cleanup');
      stopCamera();
      foodDetector.dispose();
    };

    // Add the event listener
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      console.log('FoodScanner component unmounting, performing final cleanup');

      // Remove the beforeunload event listener
      window.removeEventListener('beforeunload', handleBeforeUnload);

      // Stop camera and release resources
      stopCamera();

      // Clean up any remaining intervals
      if (detectionIntervalRef.current) {
        clearInterval(detectionIntervalRef.current);
        detectionIntervalRef.current = null;
      }

      // Clean up any remaining debounce timers
      if (foodDetectionDebounceTimer.current) {
        clearTimeout(foodDetectionDebounceTimer.current);
        foodDetectionDebounceTimer.current = null;
      }

      // Dispose of TensorFlow resources - method has its own error handling
      foodDetector.dispose();
    };
  }, []);

  // Add a special effect for when food is detected
  useEffect(() => {
    if (isFoodDetected && !window.foodDetectionUnavailable) {
      // Provide haptic feedback on mobile devices if supported
      if (navigator.vibrate && isMobile) {
        navigator.vibrate(100); // Short vibration when food is detected
      }

      // You could also play a sound here if desired
      // const sound = new Audio('/sounds/food-detected.mp3');
      // sound.play().catch(e => console.log('Could not play sound', e));
    }
  }, [isFoodDetected, isMobile]);

  const startCamera = async () => {
    try {
      setLoading(true);
      globalLoading.startLoading('camera', 'Starting camera...');

      // Check if mediaDevices API is available
      if (!navigator.mediaDevices || typeof navigator.mediaDevices.getUserMedia !== 'function') {
        throw new Error('Camera access is not supported in this browser or context');
      }

      // iOS Safari and some Android browsers work better with these constraints
      const constraints = {
        audio: false,
        video: {
          facingMode: 'environment', // Use back camera
        }
      };

      // For iOS Safari, we need to add these constraints
      if (/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream) {
        constraints.video = {
          facingMode: 'environment',
          width: { min: 0, ideal: 1920, max: 1920 },
          height: { min: 0, ideal: 1080, max: 1080 }
        };
      }

      console.log('Requesting camera access with constraints:', constraints);

      // Request camera access - this will trigger the permission prompt
      const mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
      setStream(mediaStream);

      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;

        // Make sure video is playing (important for iOS)
        videoRef.current.play().catch(playError => {
          console.error('Error playing video:', playError);
        });

        // Start food detection once the video is playing
        videoRef.current.onloadeddata = () => {
          startFoodDetection();
        };
      }

      setLoading(false);
      globalLoading.stopLoading('camera');
    } catch (err) {
      console.error('Error accessing camera:', err);

      // Handle different error types
      if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') {
        setError('Camera access was denied. Please allow camera access to use the scanner.');
      } else if (err.name === 'NotFoundError' || err.name === 'DevicesNotFoundError') {
        setError('No camera found on your device.');
      } else if (err.name === 'NotReadableError' || err.name === 'TrackStartError') {
        setError('Camera is already in use by another application.');
      } else if (err.name === 'OverconstrainedError' || err.name === 'ConstraintNotSatisfiedError') {
        // Try again with simpler constraints
        try {
          const simpleConstraints = { video: true };
          const simpleStream = await navigator.mediaDevices.getUserMedia(simpleConstraints);
          setStream(simpleStream);

          if (videoRef.current) {
            videoRef.current.srcObject = simpleStream;
            videoRef.current.play().catch(console.error);
          }

          setLoading(false);
          globalLoading.stopLoading('camera');
          return;
        } catch (simpleErr) {
          console.error('Error with simple constraints:', simpleErr);
          setError('Could not access camera with current settings.');
        }
      } else {
        setError('Could not access camera. Please check permissions and try again.');
      }

      setHasCamera(false);
      setLoading(false);
      globalLoading.stopLoading('camera');
    }
  };

  // Helper function to get the current viewfinder configuration
  // Using the imported getViewfinderConfig function from ScannerViewfinder
  const getCurrentViewfinderConfig = () => {
    return getViewfinderConfig(activeMode);
  };

  /**
   * Debounce food detection to provide stable capture button state
   * When food is detected: immediately enable capture button and reset 3-second timer
   * When food is not detected: wait 3 seconds before disabling capture button
   * @param {boolean} isDetected - Whether food is currently detected
   */
  const debounceFoodDetection = (isDetected) => {
    if (isDetected) {
      // Food detected: immediately enable capture button
      console.log('Food detection debounce: Food detected, enabling capture button immediately');
      setIsFoodDetectedStable(true);

      // Clear any existing timer and start a new 3-second countdown
      if (foodDetectionDebounceTimer.current) {
        clearTimeout(foodDetectionDebounceTimer.current);
        console.log('Food detection debounce: Cleared existing timer, resetting 3-second countdown');
      }

      // Set timer to disable capture button after 3 seconds of no detection
      foodDetectionDebounceTimer.current = setTimeout(() => {
        // Only disable if food is still not detected after 3 seconds
        // We check the current immediate detection state to avoid race conditions
        if (!isFoodDetected) {
          console.log('Food detection debounce: Disabling capture button after 3 seconds of no detection');
          setIsFoodDetectedStable(false);
        } else {
          console.log('Food detection debounce: Timer expired but food is still detected, keeping capture button enabled');
        }
        foodDetectionDebounceTimer.current = null;
      }, 3000); // 3 seconds debounce

    } else {
      // Food not detected: don't immediately disable, let the timer handle it
      // The timer will check the current state and disable if still no food after 3 seconds
      console.log('Food detection debounce: Food not detected, waiting for timer to handle disable');
    }
  };

  const startFoodDetection = () => {
    // If food detection is unavailable, set both detection states to true and return
    if (window.foodDetectionUnavailable) {
      console.log('Food detection is unavailable, enabling capture button');
      setIsFoodDetected(true);
      setIsFoodDetectedStable(true);
      setFoodDetectionActive(true);
      return;
    }

    // Only start if the model is ready and we're not already detecting
    if (!foodDetector.isReady() && !foodDetectionActive && videoRef.current) {
      console.log('Model not ready yet, will retry in 1 second');
      // Retry after a short delay
      setTimeout(startFoodDetection, 1000);
      return;
    }

    if (foodDetectionActive || !videoRef.current) {
      return;
    }

    console.log('Starting food detection');
    setFoodDetectionActive(true);

    // Run detection at regular intervals - using a faster interval for more responsive detection
    detectionIntervalRef.current = setInterval(async () => {
      if (!videoRef.current || capturedImage) {
        return;
      }

      try {
        // Get the current viewfinder configuration
        const viewfinderConfig = getCurrentViewfinderConfig();

        // Pass the viewfinder configuration to the food detector
        const result = await foodDetector.classifyFromVideo(videoRef.current, viewfinderConfig);

        if (result) {
          // Update immediate detection state
          setIsFoodDetected(result.isFoodDetected);
          setCurrentPrediction(result.topPrediction);

          // Apply debouncing to the stable detection state
          debounceFoodDetection(result.isFoodDetected);

          // Only log significant changes to avoid console spam
          if (result.isFoodDetected !== isFoodDetected) {
            console.log('Food detection changed:', result.isFoodDetected ? 'FOOD DETECTED' : 'No food detected');
          }
        }
      } catch (err) {
        console.error('Error during food detection:', err);
      }
    }, 300); // Run detection every 300ms for more responsive detection
  };

  const stopFoodDetection = () => {
    if (detectionIntervalRef.current) {
      clearInterval(detectionIntervalRef.current);
      detectionIntervalRef.current = null;
    }

    // Clear the debounce timer when stopping detection
    if (foodDetectionDebounceTimer.current) {
      clearTimeout(foodDetectionDebounceTimer.current);
      foodDetectionDebounceTimer.current = null;
    }

    setFoodDetectionActive(false);
    setIsFoodDetected(false);
    setIsFoodDetectedStable(false);
    setCurrentPrediction(null);
  };

  /**
   * Stop the camera and release all associated resources
   * This function ensures that all camera resources are properly released
   */
  const stopCamera = () => {
    try {
      console.log('Stopping camera and releasing resources');

      // Stop food detection first
      stopFoodDetection();

      // Get all active media devices to ensure we catch all streams
      const checkForActiveStreams = () => {
        if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
          navigator.mediaDevices.enumerateDevices()
            .then(devices => {
              console.log(`Found ${devices.length} media devices`);
            })
            .catch(err => {
              console.error('Error enumerating devices:', err);
            });
        }
      };

      // Clear video source
      if (videoRef.current) {
        try {
          // Pause the video first
          videoRef.current.pause();

          // Remove the srcObject
          videoRef.current.srcObject = null;
          console.log('Cleared video source');
        } catch (videoErr) {
          console.error('Error clearing video source:', videoErr);
        }
      }

      // Stop all tracks in the stream
      if (stream) {
        try {
          const tracks = stream.getTracks();
          console.log(`Stopping ${tracks.length} media tracks`);

          tracks.forEach(track => {
            try {
              // Log track info for debugging
              console.log(`Stopping track: ${track.kind}, enabled: ${track.enabled}, readyState: ${track.readyState}`);

              // First disable the track
              track.enabled = false;

              // Then stop it
              track.stop();

              console.log(`Track stopped: ${track.kind}`);
            } catch (trackErr) {
              console.error(`Error stopping track ${track.kind}:`, trackErr);
            }
          });

          // Clear the stream reference
          setStream(null);
          console.log('Stream reference cleared');
        } catch (streamErr) {
          console.error('Error stopping stream tracks:', streamErr);
        }
      } else {
        console.log('No stream to stop');
      }

      // Double-check for any active streams after cleanup
      setTimeout(checkForActiveStreams, 500);

    } catch (err) {
      console.error('Error stopping camera:', err);
    } finally {
      // Always make sure we clear the stream reference
      setStream(null);
    }
  };

  const captureImage = () => {
    if (!videoRef.current || !canvasRef.current) return;

    // Check if food detection is unavailable (set by FoodDetector when TensorFlow.js fails to load)
    const foodDetectionUnavailable = window.foodDetectionUnavailable;

    // Strict check: Only allow capture if food is detected in scan mode (using stable detection)
    // This is a double-check in case the button is somehow clicked when disabled
    // Skip this check if food detection is unavailable
    if (activeMode === 'scan' && !isFoodDetectedStable && !foodDetectionUnavailable) {
      setError('No food detected. Please point the camera at a food item.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');

      // Get the current viewfinder configuration
      const viewfinderConfig = getCurrentViewfinderConfig();

      // Set canvas dimensions based on the video dimensions
      const videoWidth = video.videoWidth || video.offsetWidth || 640;
      const videoHeight = video.videoHeight || video.offsetHeight || 480;

      // Parse percentage values from viewfinder config
      const viewfinderWidthPercent = parseFloat(viewfinderConfig.width) / 100;

      // Calculate the actual pixel dimensions of the viewfinder
      const viewfinderWidth = videoWidth * viewfinderWidthPercent;

      // For square viewfinders, calculate height based on width to maintain 1:1 aspect ratio
      let viewfinderHeight;
      if (viewfinderConfig.maintainSquare) {
        // Make it square by using the same pixel dimension as width
        viewfinderHeight = viewfinderWidth;
        console.log(`Square viewfinder: using width ${viewfinderWidth}px for both dimensions`);
      } else {
        // Use the configured height percentage
        const viewfinderHeightPercent = parseFloat(viewfinderConfig.height) / 100;
        viewfinderHeight = videoHeight * viewfinderHeightPercent;
        console.log(`Rectangle viewfinder: ${viewfinderWidth}x${viewfinderHeight}px`);
      }

      // Calculate the position of the viewfinder (centered horizontally, at 25% from the top)
      const viewfinderX = (videoWidth - viewfinderWidth) / 2;
      const viewfinderY = videoHeight * 0.25; // 25% from the top as specified in ScannerViewfinder.jsx

      // Set the canvas size to match the viewfinder dimensions
      canvas.width = viewfinderWidth;
      canvas.height = viewfinderHeight;

      // Draw only the viewfinder area to the canvas
      context.drawImage(
        video,
        viewfinderX, viewfinderY, viewfinderWidth, viewfinderHeight, // Source rectangle
        0, 0, viewfinderWidth, viewfinderHeight // Destination rectangle
      );

      console.log(`Capturing cropped image from viewfinder: ${viewfinderWidth}x${viewfinderHeight} at (${viewfinderX},${viewfinderY})`);

      // Create a URL for the captured image to display
      const capturedImageUrl = canvas.toDataURL('image/jpeg', 0.95);
      setCapturedImage(capturedImageUrl);

      // Stop the camera after capturing the image
      stopCamera();

      // For iOS Safari, we need to use a different approach to get the image data
      if (/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream) {
        try {
          // Convert base64 to blob
          const byteString = atob(capturedImageUrl.split(',')[1]);
          const mimeString = capturedImageUrl.split(',')[0].split(':')[1].split(';')[0];
          const ab = new ArrayBuffer(byteString.length);
          const ia = new Uint8Array(ab);

          for (let i = 0; i < byteString.length; i++) {
            ia[i] = byteString.charCodeAt(i);
          }

          const blob = new Blob([ab], { type: mimeString });
          processImageBlob(blob);
        } catch (iosError) {
          console.error('Error capturing image on iOS:', iosError);
          // Fallback to the standard method
          canvas.toBlob(blob => processImageBlob(blob), 'image/jpeg', 0.95);
        }
      } else {
        // Standard approach for other browsers
        canvas.toBlob(blob => processImageBlob(blob), 'image/jpeg', 0.95);
      }
    } catch (err) {
      console.error('Error capturing image:', err);
      setError('Failed to capture image. Please try again.');
      setLoading(false);
    }
  };

  // Function to retake the image
  const retakeImage = () => {
    // Clear the captured image
    setCapturedImage(null);
    // Restart the camera
    startCamera();
  };

  // Helper function to process the image blob
  const processImageBlob = async (blob) => {
    if (!blob) {
      setError('Failed to capture image. Please try again.');
      setLoading(false);
      return;
    }

    try {
      // Create a temporary blob URL for immediate display
      const tempBlobUrl = URL.createObjectURL(blob);

      // Generate a unique ID for this analyzing food item
      const analyzingId = `analyzing_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Create the analyzing food item data
      const analyzingFoodItem = {
        id: analyzingId,
        name: 'Analyzing food...',
        calories: 0,
        protein_g: 0,
        carbs_g: 0,
        fats_g: 0,
        fiber_g: 0,
        sugar_g: 0,
        sodium_mg: 0,
        cholesterol_mg: 0,
        saturated_fat_g: 0,
        trans_fat_g: 0,
        vitamin_c_mg: 0,
        calcium_mg: 0,
        iron_mg: 0,
        potassium_mg: 0,
        vitamin_d_mcg: 0,
        serving_size: 1,
        items: [],
        image_url: tempBlobUrl,
        timestamp: new Date().toISOString(),
        isAnalyzing: true,
        apiProvider: apiConfig.provider // Add API provider info for proper component selection
        // Note: Don't store the blob in localStorage as it can't be serialized
      };

      // Store the analyzing item in localStorage for the dashboard to pick up
      const existingAnalyzingItems = JSON.parse(localStorage.getItem('analyzingFoodItems') || '[]');
      existingAnalyzingItems.push(analyzingFoodItem);
      localStorage.setItem('analyzingFoodItems', JSON.stringify(existingAnalyzingItems));

      // Trigger a custom event to notify the dashboard
      window.dispatchEvent(new CustomEvent('newAnalyzingFood', {
        detail: analyzingFoodItem
      }));

      // Make sure to stop the camera and clean up resources before closing
      stopCamera();

      // Close the scanner and navigate to home immediately
      onClose();
      navigate('/');

      // Start the background analysis process
      setTimeout(() => {
        analyzeFood(blob, analyzingId, tempBlobUrl);
      }, 100);

      // Clean up the blob URL after a reasonable time to prevent memory leaks
      setTimeout(() => {
        if (tempBlobUrl && tempBlobUrl.startsWith('blob:')) {
          URL.revokeObjectURL(tempBlobUrl);
          console.log('Cleaned up blob URL:', tempBlobUrl);
        }
      }, 30000); // Clean up after 30 seconds

    } catch (err) {
      console.error('Error processing captured image:', err);
      setError('Failed to process image. Please try again.');
      setLoading(false);
    }
  };

  // Background food analysis function
  const analyzeFood = async (blob, analyzingId, tempBlobUrl) => {
    try {
      console.log('Starting food analysis...');
      console.log('Blob type:', blob.type);
      console.log('Blob size:', blob.size);

      // Get API configuration for progress tracking
      const apiConfig = getApiConfig();

      // Start Ollama progress tracking if using Ollama
      if (apiConfig.provider === 'ollama') {
        ollamaProgressService.startTracking(analyzingId, {
          onProgress: (progress) => {
            console.log('Ollama progress update:', progress);
          },
          onComplete: (result) => {
            console.log('Ollama analysis complete:', result);
          },
          onError: (error) => {
            console.error('Ollama analysis error:', error);
            window.dispatchEvent(new CustomEvent('foodAnalysisError', {
              detail: { analyzingId, error: error.message }
            }));
          }
        });
      }

      // Ensure the blob has the correct MIME type
      let processedBlob = blob;
      if (!blob.type || blob.type === '' || !blob.type.startsWith('image/')) {
        // Create a new blob with the correct MIME type
        processedBlob = new Blob([blob], { type: 'image/jpeg' });
        console.log('Fixed blob MIME type to image/jpeg');
      }

      // Create form data for image upload
      const uploadFormData = new FormData();
      uploadFormData.append('image', processedBlob, 'food.jpg');

      // Get auth token from localStorage
      const token = localStorage.getItem('token');

      // First, upload the image to the server to get a permanent URL
      let serverImageUrl = null;
      try {
        const uploadResponse = await axios.post('/api/uploads/meal-image', uploadFormData, {
          headers: token ? { Authorization: `Bearer ${token}` } : undefined
        });
        console.log('Image upload response:', uploadResponse.data);
        if (uploadResponse.data && uploadResponse.data.imageUrl) {
          serverImageUrl = uploadResponse.data.imageUrl;
          console.log('Server provided absolute image URL:', serverImageUrl);
        }
      } catch (uploadError) {
        console.error('Error uploading image:', uploadError);
        // Continue with analysis even if image upload fails
      }

      // Check if API is configured
      if (!isConfigValid()) {
        throw new Error('API not configured. Please configure your API settings in the Settings page.');
      }

      // Create a separate FormData for analysis (FormData can only be consumed once)
      const analysisFormData = new FormData();
      analysisFormData.append('image', processedBlob, 'food.jpg');
      analysisFormData.append('apiProvider', apiConfig.provider);
      analysisFormData.append('apiModel', apiConfig.model);

      // Add credentials based on provider type
      if (apiConfig.apiKey) {
        analysisFormData.append('apiKey', apiConfig.apiKey);
      }
      if (apiConfig.clientId) {
        analysisFormData.append('clientId', apiConfig.clientId);
      }
      if (apiConfig.clientSecret) {
        analysisFormData.append('clientSecret', apiConfig.clientSecret);
      }

      // Send to backend for analysis using client-side API
      let foodData;
      try {
        // Log the FormData contents for debugging
        console.log('=== ANALYSIS FORMDATA DEBUG ===');
        for (let [key, value] of analysisFormData.entries()) {
          console.log(`${key}:`, value);
          if (value instanceof Blob) {
            console.log(`  - Blob size: ${value.size} bytes`);
            console.log(`  - Blob type: ${value.type}`);
          }
        }

        // Use the new client-side analyze endpoint
        const response = await axios.post('/api/analyze/client', analysisFormData, {
          headers: token ? { Authorization: `Bearer ${token}` } : undefined
        });
        foodData = response.data;
        console.log('Food analysis response:', foodData);

        // Check if we got the raw API response instead of the processed one
        if (foodData.nutrition_analysis && foodData.status === 'success') {
          const nutritionData = foodData.nutrition_analysis;
          // Convert to the expected format
          foodData = {
            food_name: nutritionData.name,
            calories: nutritionData.calories,
            protein_g: nutritionData.protein,
            carbs_g: nutritionData.carbs,
            fats_g: nutritionData.fat,
            fiber_g: nutritionData.fiber,
            sugar_g: nutritionData.sugar,
            sodium_mg: nutritionData.sodium,
            cholesterol_mg: nutritionData.cholesterol,
            saturated_fat_g: nutritionData.saturated_fat,
            trans_fat_g: nutritionData.trans_fat,
            vitamin_c_mg: nutritionData.vitamin_c,
            calcium_mg: nutritionData.calcium,
            iron_mg: nutritionData.iron,
            potassium_mg: nutritionData.potassium,
            vitamin_d_mcg: nutritionData.vitamin_d,
            serving_size: nutritionData.serving_size,
            items: nutritionData.items || []
          };
        }
      } catch (apiError) {
        console.error('API call failed:', apiError);

        // Fail Ollama progress tracking if using Ollama
        if (apiConfig.provider === 'ollama') {
          ollamaProgressService.failRequest(analyzingId, apiError);
        }

        // Notify the dashboard that analysis failed
        window.dispatchEvent(new CustomEvent('foodAnalysisError', {
          detail: { analyzingId, error: apiError.message }
        }));
        return;
      }



      // Create the analyzed food data
      const analyzedFood = {
        name: foodData.food_name,
        calories: foodData.calories,
        protein_g: foodData.protein_g || 0,
        carbs_g: foodData.carbs_g || 0,
        fats_g: foodData.fats_g || 0,
        fiber_g: foodData.fiber_g || 0,
        sugar_g: foodData.sugar_g || 0,
        sodium_mg: foodData.sodium_mg || 0,
        cholesterol_mg: foodData.cholesterol_mg || 0,
        saturated_fat_g: foodData.saturated_fat_g || 0,
        trans_fat_g: foodData.trans_fat_g || 0,
        vitamin_c_mg: foodData.vitamin_c_mg || 0,
        calcium_mg: foodData.calcium_mg || 0,
        iron_mg: foodData.iron_mg || 0,
        potassium_mg: foodData.potassium_mg || 0,
        vitamin_d_mcg: foodData.vitamin_d_mcg || 0,
        serving_size: foodData.serving_size || 1,
        items: foodData.items || [],
        image_url: serverImageUrl || tempBlobUrl,
        is_server_image: !!serverImageUrl
      };

      // Complete Ollama progress tracking if using Ollama
      if (apiConfig.provider === 'ollama') {
        ollamaProgressService.completeRequest(analyzingId, analyzedFood);
      }

      // Check if this is sample data before dispatching
      if (analyzedFood.name === 'Grilled Salmon' && analyzedFood.calories === 420) {
        console.error('===== FOODSCANNER: ABOUT TO DISPATCH GRILLED SALMON SAMPLE DATA =====');
        console.error('This is where the sample data is being dispatched from!');
        console.error('Stack trace:', new Error().stack);
        console.error('Analyzed food data:', JSON.stringify(analyzedFood, null, 2));
        console.error('Food data source:', JSON.stringify(foodData, null, 2));
      }

      // Notify the dashboard that analysis is complete
      console.log('===== DISPATCHING FOOD ANALYSIS COMPLETE =====');
      console.log('Analyzing ID:', analyzingId);
      console.log('Analyzed Food being dispatched:', JSON.stringify(analyzedFood, null, 2));

      window.dispatchEvent(new CustomEvent('foodAnalysisComplete', {
        detail: { analyzingId, analyzedFood }
      }));

    } catch (err) {
      console.error('Error analyzing food:', err);

      // Fail Ollama progress tracking if using Ollama
      const currentApiConfig = getApiConfig();
      if (currentApiConfig && currentApiConfig.provider === 'ollama') {
        ollamaProgressService.failRequest(analyzingId, err);
      }

      // Notify the dashboard that analysis failed
      window.dispatchEvent(new CustomEvent('foodAnalysisError', {
        detail: { analyzingId, error: err.message }
      }));
    }
  };

  const handleGalleryUpload = () => {
    try {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';

      input.onchange = async (e) => {
        if (!e.target.files || !e.target.files[0]) return;

        setLoading(true);
        setError(null);

        const file = e.target.files[0];

        try {
          console.log('Gallery file upload - File type:', file.type);
          console.log('Gallery file upload - File size:', file.size);
          console.log('Gallery file upload - File name:', file.name);

          // Create form data for image upload
          const uploadFormData = new FormData();
          uploadFormData.append('image', file);

          // Get auth token from localStorage
          const token = localStorage.getItem('token');

          // First, upload the image to the server to get a permanent URL
          let serverImageUrl = null;
          try {
            const uploadResponse = await axios.post('/api/uploads/meal-image', uploadFormData, {
              headers: token ? { Authorization: `Bearer ${token}` } : undefined
            });
            console.log('Image upload response (gallery):', uploadResponse.data);
            if (uploadResponse.data && uploadResponse.data.imageUrl) {
              // Use the absolute URL returned by the server
              serverImageUrl = uploadResponse.data.imageUrl;
              console.log('Server provided absolute image URL (gallery):', serverImageUrl);
            }
          } catch (uploadError) {
            console.error('Error uploading gallery image:', uploadError);
            // Continue with analysis even if image upload fails
          }

          // Check if API is configured
          if (!isConfigValid()) {
            throw new Error('API not configured. Please configure your API settings in the Settings page.');
          }

          // Get API configuration
          const apiConfig = getApiConfig();

          // Create a separate FormData for analysis (FormData can only be consumed once)
          const analysisFormData = new FormData();
          analysisFormData.append('image', file);
          analysisFormData.append('apiProvider', apiConfig.provider);
          analysisFormData.append('apiModel', apiConfig.model);

          // Add credentials based on provider type
          if (apiConfig.apiKey) {
            analysisFormData.append('apiKey', apiConfig.apiKey);
          }
          if (apiConfig.clientId) {
            analysisFormData.append('clientId', apiConfig.clientId);
          }
          if (apiConfig.clientSecret) {
            analysisFormData.append('clientSecret', apiConfig.clientSecret);
          }

          // Send to backend for analysis using client-side API
          let foodData;
          try {
            console.log('=== GALLERY ANALYSIS FORMDATA DEBUG ===');
            for (let [key, value] of analysisFormData.entries()) {
              console.log(`${key}:`, value);
              if (value instanceof File) {
                console.log(`  - File size: ${value.size} bytes`);
                console.log(`  - File type: ${value.type}`);
                console.log(`  - File name: ${value.name}`);
              }
            }

            // Use the new client-side analyze endpoint
            const response = await axios.post('/api/analyze/client', analysisFormData, {
              headers: token ? { Authorization: `Bearer ${token}` } : undefined
            });
            foodData = response.data;
            console.log('Food analysis response (gallery upload):', foodData);

            // Check if we got the raw API response instead of the processed one
            if (foodData.nutrition_analysis && foodData.status === 'success') {
              const nutritionData = foodData.nutrition_analysis;
              // Convert to the expected format
              foodData = {
                food_name: nutritionData.name,
                calories: nutritionData.calories,
                protein_g: nutritionData.protein,
                carbs_g: nutritionData.carbs,
                fats_g: nutritionData.fat,
                fiber_g: nutritionData.fiber,
                sugar_g: nutritionData.sugar,
                sodium_mg: nutritionData.sodium,
                cholesterol_mg: nutritionData.cholesterol,
                saturated_fat_g: nutritionData.saturated_fat,
                trans_fat_g: nutritionData.trans_fat,
                vitamin_c_mg: nutritionData.vitamin_c,
                calcium_mg: nutritionData.calcium,
                iron_mg: nutritionData.iron,
                potassium_mg: nutritionData.potassium,
                vitamin_d_mcg: nutritionData.vitamin_d,
                serving_size: nutritionData.serving_size,
                items: nutritionData.items || []
              };
            }

            // Log the processed food data
            console.log('Processed food data:', foodData);
          } catch (apiError) {
            console.error('API call failed:', apiError);
            setError(`Analysis failed: ${apiError.message}`);
            setLoading(false);
            return;
          }



          // Create a temporary blob URL for preview if server upload failed
          const tempBlobUrl = serverImageUrl ? null : URL.createObjectURL(file);

          // Log image URL information for debugging
          console.log('===== IMAGE URL DEBUG (GALLERY) =====');
          console.log('Server image URL:', serverImageUrl);
          console.log('Temp blob URL:', tempBlobUrl);
          console.log('Using URL:', serverImageUrl || tempBlobUrl);

          // Create the analyzed food data for immediate processing
          const analyzedFood = {
            name: foodData.food_name,
            calories: foodData.calories,
            protein_g: foodData.protein_g || 0,
            carbs_g: foodData.carbs_g || 0,
            fats_g: foodData.fats_g || 0,
            fiber_g: foodData.fiber_g || 0,
            sugar_g: foodData.sugar_g || 0,
            sodium_mg: foodData.sodium_mg || 0,
            cholesterol_mg: foodData.cholesterol_mg || 0,
            saturated_fat_g: foodData.saturated_fat_g || 0,
            trans_fat_g: foodData.trans_fat_g || 0,
            vitamin_c_mg: foodData.vitamin_c_mg || 0,
            calcium_mg: foodData.calcium_mg || 0,
            iron_mg: foodData.iron_mg || 0,
            potassium_mg: foodData.potassium_mg || 0,
            vitamin_d_mcg: foodData.vitamin_d_mcg || 0,
            serving_size: foodData.serving_size || 1,
            items: foodData.items || [],
            image_url: serverImageUrl || tempBlobUrl,
            is_server_image: !!serverImageUrl
          };

          // Generate a unique ID for this analyzing food item
          const analyzingId = `analyzing_gallery_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

          // Create the analyzing food item data
          const analyzingFoodItem = {
            id: analyzingId,
            name: 'Analyzing food...',
            calories: 0,
            protein_g: 0,
            carbs_g: 0,
            fats_g: 0,
            fiber_g: 0,
            sugar_g: 0,
            sodium_mg: 0,
            cholesterol_mg: 0,
            saturated_fat_g: 0,
            trans_fat_g: 0,
            vitamin_c_mg: 0,
            calcium_mg: 0,
            iron_mg: 0,
            potassium_mg: 0,
            vitamin_d_mcg: 0,
            serving_size: 1,
            items: [],
            image_url: serverImageUrl || tempBlobUrl,
            timestamp: new Date().toISOString(),
            isAnalyzing: true,
            apiProvider: apiConfig.provider // Add API provider info for proper component selection
          };

          // Store the analyzing item in localStorage for the dashboard to pick up
          const existingAnalyzingItems = JSON.parse(localStorage.getItem('analyzingFoodItems') || '[]');
          existingAnalyzingItems.push(analyzingFoodItem);
          localStorage.setItem('analyzingFoodItems', JSON.stringify(existingAnalyzingItems));

          // Trigger a custom event to notify the dashboard
          window.dispatchEvent(new CustomEvent('newAnalyzingFood', {
            detail: analyzingFoodItem
          }));

          // Make sure to stop the camera and clean up resources before closing
          stopCamera();

          // Close the scanner and navigate to home immediately
          onClose();
          navigate('/');

          // Immediately trigger analysis complete since we already have the data
          setTimeout(() => {
            window.dispatchEvent(new CustomEvent('foodAnalysisComplete', {
              detail: { analyzingId, analyzedFood }
            }));
          }, 1000); // Small delay to show the analyzing animation
        } catch (err) {
          console.error('Error analyzing food:', err);
          setError('Failed to analyze food. Please try again.');
          setLoading(false);
        }
      };

      // Trigger file input click
      input.click();
    } catch (err) {
      console.error('Error opening file picker:', err);
      setError('Could not open file picker. Please try again or use a different browser.');

      // Fallback for devices that don't support file input
      setTimeout(() => {
        // Make sure to stop the camera and clean up resources before navigating
        stopCamera();

        navigate('/add-meal');
        onClose();
      }, 2000);
    }
  };

  // Handle modal close event
  const handleModalClose = () => {
    console.log('Modal close button clicked, cleaning up resources');

    // Make sure to stop the camera and clean up resources before closing
    stopCamera();

    // Dispose of TensorFlow resources
    foodDetector.dispose();

    // Call the parent's onClose handler
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleModalClose}
      fullScreen={true}
      showCloseButton={true}
    >
      <div className="relative h-full w-full bg-black animate-fadeIn full-screen-cover scanner-container" style={{
          top: 0,
          height: '100%',
          paddingTop: 0,
          marginTop: 0,
          width: '100%',
          position: 'relative',
          left: 0,
          right: 0
        }}>
        {/* iOS notch area is now managed by NotchAreaManager component */}

        {/* Camera view or Captured Image */}
        {capturedImage ? (
          /* Show captured image */
          <img
            src={capturedImage}
            alt="Captured food"
            className="absolute inset-0 h-full w-full object-cover scanner-image"
            style={{
              height: 'calc(100% + env(safe-area-inset-top, 0))',
              top: 'calc(-1 * env(safe-area-inset-top, 0))',
              width: '100%',
              position: 'absolute',
              left: 0,
              right: 0
            }}
          />
        ) : isMobile && hasCamera ? (
          /* Show live camera feed */
          <video
            ref={videoRef}
            autoPlay
            playsInline
            muted
            controls={false}
            className="absolute inset-0 h-full w-full object-cover scanner-video"
            style={{
              height: 'calc(100% + env(safe-area-inset-top, 0))',
              top: 'calc(-1 * env(safe-area-inset-top, 0))',
              width: '100%',
              position: 'absolute',
              left: 0,
              right: 0
            }}
          />
        ) : (
          /* Show camera not available message */
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-white text-center p-6 max-w-md">
              <div className="bg-black bg-opacity-50 p-6 rounded-xl">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <h3 className="text-xl font-bold mb-2">Camera Not Available</h3>
                <p className="mb-4">
                  {isMobile
                    ? "Camera access is required for scanning food. Please check your camera permissions in your browser settings."
                    : "Food scanning is only available on mobile devices."}
                </p>
                <div className="flex flex-col space-y-2">
                  <button
                    onClick={handleGalleryUpload}
                    className="px-4 py-2 bg-white text-black rounded-lg font-medium"
                  >
                    Upload from Gallery Instead
                  </button>
                  <button
                    onClick={onClose}
                    className="px-4 py-2 bg-transparent border border-white text-white rounded-lg"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Hidden canvas for capturing images */}
        <canvas ref={canvasRef} className="hidden" />

        {/* Viewfinder - only show when camera is active and no image is captured */}
        {!capturedImage && (
          <ScannerViewfinder
            mode={activeMode}
            isFoodDetected={isFoodDetectedStable}
            foodDetectionActive={foodDetectionActive}
          />
        )}

        {/* Help button */}
        <button
          className="absolute safe-area-inset-top z-10 p-2 rounded-full bg-black bg-opacity-40 text-white border border-white border-opacity-30 safe-area-margin-top"
          style={{ top: 'calc(env(safe-area-inset-top, 0) + 1rem)', right: '1rem' }}
          aria-label="Help"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </button>

        {/* Mode buttons - positioned in the middle between viewfinder and bottom controls */}
        {(isMobile || hasCamera) && hasCamera && !capturedImage && (
          <div className="absolute left-0 right-0 z-10" style={{
            top: '62.5%', // Positioned exactly in the middle between viewfinder (ends ~50%) and bottom controls (starts ~75%)
            transform: 'translateY(-50%)', // Center the element on the calculated position
          }}>
            <div className="flex justify-center mx-auto">
              <div className="flex space-x-2 px-2 w-full max-w-md justify-between">
                <ScannerButton
                  icon="scan"
                  label="Scan Food"
                  active={activeMode === 'scan'}
                  onClick={() => {
                    setActiveMode('scan');
                    // Make sure food detection is active in scan mode
                    if (!foodDetectionActive) {
                      startFoodDetection();
                    }
                  }}
                />
                <ScannerButton
                  icon="barcode"
                  label="Barcode"
                  active={activeMode === 'barcode'}
                  onClick={() => {
                    // Show "Coming Soon" overlay instead of changing mode
                    setComingSoonFeature('Barcode Scanner');
                    setShowComingSoon(true);
                  }}
                />
                <ScannerButton
                  icon="label"
                  label="Food Label"
                  active={activeMode === 'label'}
                  onClick={() => {
                    // Show "Coming Soon" overlay instead of changing mode
                    setComingSoonFeature('Food Label Scanner');
                    setShowComingSoon(true);
                  }}
                />
                <ScannerButton
                  icon="nutrition"
                  label="Nutrition facts"
                  active={activeMode === 'nutrition'}
                  onClick={() => {
                    // Show "Coming Soon" overlay instead of changing mode
                    setComingSoonFeature('Nutrition Facts Scanner');
                    setShowComingSoon(true);
                  }}
                />
              </div>
            </div>
          </div>
        )}

        {/* Bottom controls - only show when camera is available or on mobile */}
        {(isMobile || hasCamera) && (
          <div className="absolute bottom-0 left-0 right-0 p-4 pt-8 pb-32 safe-area-padding-bottom bg-gradient-to-t from-black/80 to-transparent z-10">
            {/* Capture and gallery buttons */}
            <div className="flex justify-between items-center w-full">
              {/* Show different buttons based on whether an image has been captured */}
              {capturedImage ? (
                /* Buttons for captured image state */
                <div className="flex justify-between items-center w-full px-4">
                  {/* Retake button */}
                  <button
                    onClick={retakeImage}
                    disabled={loading}
                    className="scanner-control-button text-white"
                    aria-label="Retake photo"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    <span className="sr-only">Retake</span>
                  </button>

                  {/* Status indicator - shows loading dots or checkmark */}
                  <div className={`scanner-capture-button ${loading ? 'opacity-70' : ''}`}>
                    {loading ? (
                      <div className="w-12 h-12 rounded-full bg-white flex items-center justify-center">
                        <div className="flex space-x-1">
                          <div className="w-1 h-1 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '0ms' }}></div>
                          <div className="w-1 h-1 bg-green-500 rounded-full animate-pulse" style={{ animationDelay: '200ms' }}></div>
                          <div className="w-1 h-1 bg-yellow-500 rounded-full animate-pulse" style={{ animationDelay: '400ms' }}></div>
                        </div>
                      </div>
                    ) : (
                      <div className="w-12 h-12 rounded-full bg-white flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                    )}
                  </div>

                  {/* Cancel button */}
                  <button
                    onClick={onClose}
                    disabled={loading}
                    className="scanner-control-button text-white"
                    aria-label="Cancel"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    <span className="sr-only">Cancel</span>
                  </button>
                </div>
              ) : (
                /* Buttons for camera view state */
                <>
                  {/* From Gallery button - positioned at left edge, aligned with close button */}
                  <button
                    onClick={handleGalleryUpload}
                    className="scanner-control-button text-white ml-4"
                    aria-label="Upload from gallery"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span className="sr-only">From Gallery</span>
                  </button>

                  {/* Only show capture button when camera is available - centered */}
                  {hasCamera ? (
                    <button
                      onClick={captureImage}
                      disabled={loading || (activeMode === 'scan' && !isFoodDetectedStable && !window.foodDetectionUnavailable)}
                      className={`scanner-capture-button ${
                        loading ? 'opacity-50' :
                        (activeMode === 'scan' && !isFoodDetectedStable && !window.foodDetectionUnavailable) ? 'food-not-detected' : ''
                      }`}
                      aria-label="Capture image"
                      title="Capture image"
                    >
                      <div className="w-12 h-12 rounded-full bg-white transition-colors duration-300"></div>
                    </button>
                  ) : (
                    <button
                      onClick={onClose}
                      className="px-4 py-2 bg-white text-black rounded-lg font-medium"
                    >
                      Close
                    </button>
                  )}

                  {/* Flash button - positioned at right edge, aligned with help button */}
                  <button
                    className="scanner-control-button text-white mr-4"
                    aria-label="Toggle flash"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </button>
                </>
              )}
            </div>
          </div>
        )}



        {/* Error message */}
        {error && (
          <div className="absolute top-1/4 left-0 right-0 mx-auto w-5/6 max-w-md bg-red-500 text-white px-4 py-3 rounded-xl shadow-lg z-20">
            <div className="flex items-center justify-between">
              <span>{error}</span>
              <button onClick={() => setError(null)} className="text-white">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        )}

        {/* Coming Soon overlay */}
        {showComingSoon && (
          <ComingSoonOverlay
            feature={comingSoonFeature}
            onClose={() => setShowComingSoon(false)}
          />
        )}
      </div>
    </Modal>
  );
};

export default FoodScanner;
