const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
const FormData = require('form-data');
const { body, validationResult } = require('express-validator');
const { authenticate } = require('../middleware/auth');

const router = express.Router();

// Configure multer for file uploads - using disk storage for analysis
const storage = multer.diskStorage({
  destination: (_req, _file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/analyze');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (_req, file, cb) => {
    const ext = path.extname(file.originalname);
    cb(null, `${uuidv4()}${ext}`);
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (_req, file, cb) => {
    console.log('=== FILE FILTER DEBUG (ANALYZE) ===');
    console.log('Original name:', file.originalname);
    console.log('MIME type:', file.mimetype);
    console.log('Field name:', file.fieldname);

    const allowedTypes = /jpeg|jpg|png|gif/;
    const ext = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    console.log('Extension test result:', ext);
    console.log('MIME type test result:', mimetype);

    if (ext && mimetype) {
      console.log('File accepted');
      return cb(null, true);
    }

    console.log('File rejected - Only image files are allowed');
    cb(new Error('Only image files are allowed'));
  }
});

// Helper function to wait for file to be fully written and verify it's not corrupted
const waitForFileAndVerify = async (filePath, maxRetries = 10, retryDelay = 100) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        if (stats.size > 0) {
          // File exists and has content, try to read it
          const buffer = fs.readFileSync(filePath);
          if (buffer.length > 0) {
            console.log(`File verified successfully after ${i + 1} attempts, size: ${buffer.length} bytes`);
            return buffer;
          }
        }
      }
      console.log(`Attempt ${i + 1}: File not ready yet (size: ${fs.existsSync(filePath) ? fs.statSync(filePath).size : 'not found'}), waiting...`);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    } catch (error) {
      console.log(`Attempt ${i + 1}: Error reading file: ${error.message}, waiting...`);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }
  throw new Error('File was not written properly or is corrupted after multiple attempts');
};

/**
 * @route POST /api/analyze/client
 * @desc Analyze food image using client-provided API key
 * @access Private
 */
router.post('/client',
  authenticate,
  upload.single('image'),
  [
    body('apiProvider').isIn(['openai', 'gemini', 'ollama']).withMessage('API provider must be openai, gemini, or ollama'),
    body('apiModel').isString().withMessage('API model is required'),
    body('apiKey').optional().isString().withMessage('API key must be a string'),
    body('clientId').optional().isString().withMessage('Client ID must be a string'),
    body('clientSecret').optional().isString().withMessage('Client Secret must be a string')
  ],
  async (req, res) => {
    try {
      // Validate request
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      if (!req.file) {
        return res.status(400).json({ error: 'No image file provided' });
      }

      const { apiProvider, apiModel, apiKey, clientId, clientSecret } = req.body;

      // Validate credentials based on provider
      if (apiProvider === 'ollama') {
        if (!clientId || !clientSecret) {
          return res.status(400).json({ error: 'Client ID and Client Secret are required for Ollama' });
        }
      } else {
        if (!apiKey) {
          return res.status(400).json({ error: 'API key is required for OpenAI and Gemini' });
        }
      }

      // Get the file path where multer saved the file
      const imagePath = req.file.path;
      console.log(`File saved to: ${imagePath}`);

      // Wait for the file to be fully written and verify it's not corrupted
      let fileBuffer;
      try {
        fileBuffer = await waitForFileAndVerify(imagePath);
      } catch (error) {
        console.error('File verification failed:', error.message);
        return res.status(400).json({
          error: 'File upload failed',
          details: 'The uploaded file appears to be corrupted or was not saved properly'
        });
      }

      // Convert image to base64
      const imageBase64 = fileBuffer.toString('base64');

      let analysisResult;

      if (apiProvider === 'openai') {
        analysisResult = await analyzeWithOpenAI(imageBase64, apiModel, apiKey);
      } else if (apiProvider === 'gemini') {
        analysisResult = await analyzeWithGemini(imageBase64, apiModel, apiKey);
      } else if (apiProvider === 'ollama') {
        analysisResult = await analyzeWithOllama(imageBase64, apiModel, { clientId, clientSecret });
      } else {
        return res.status(400).json({ error: 'Unsupported API provider' });
      }

      // Clean up the temporary file
      fs.unlink(imagePath, (err) => {
        if (err) console.error('Error deleting temporary file:', err);
        else console.log('Temporary file cleaned up successfully');
      });

      res.json(analysisResult);

    } catch (error) {
      console.error('Client-side image analysis error:', error);

      // Clean up the temporary file in case of error
      if (req.file && req.file.path) {
        fs.unlink(req.file.path, (err) => {
          if (err) console.error('Error deleting temporary file after error:', err);
          else console.log('Temporary file cleaned up after error');
        });
      }

      res.status(500).json({
        error: 'Failed to analyze image',
        details: error.message
      });
    }
  }
);



// Helper function to analyze image with OpenAI
async function analyzeWithOpenAI(imageBase64, model, apiKey) {
  const prompt = "Analyze this food image and provide detailed nutritional information in JSON format.";

  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      model: model,
      messages: [
        {
          role: 'user',
          content: [
            { type: 'text', text: prompt },
            {
              type: 'image_url',
              image_url: { url: `data:image/jpeg;base64,${imageBase64}` }
            }
          ]
        }
      ],
      response_format: {
        type: "json_schema",
        json_schema: {
          name: "nutrition_analysis",
          schema: {
            type: "object",
            properties: {
              name: { type: "string" },
              calories: { type: "number" },
              protein: { type: "number" },
              fat: { type: "number" },
              carbs: { type: "number" },
              fiber: { type: "number" },
              sugar: { type: "number" },
              sodium: { type: "number" },
              cholesterol: { type: "number" },
              saturated_fat: { type: "number" },
              trans_fat: { type: "number" },
              vitamin_c: { type: "number" },
              calcium: { type: "number" },
              iron: { type: "number" },
              potassium: { type: "number" },
              vitamin_d: { type: "number" },
              serving_size: { type: "string" },
              items: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    name: { type: "string" },
                    calories: { type: "number" },
                    portion: { type: "string" }
                  }
                }
              }
            },
            required: ["name", "calories", "protein", "fat", "carbs", "fiber", "sugar", "sodium", "cholesterol", "saturated_fat", "trans_fat", "vitamin_c", "calcium", "iron", "potassium", "vitamin_d", "serving_size", "items"]
          }
        }
      }
    })
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(`OpenAI API error: ${errorData.error?.message || 'Unknown error'}`);
  }

  const data = await response.json();
  const nutritionData = JSON.parse(data.choices[0].message.content);

  // Format response to match expected format
  return {
    food_name: nutritionData.name,
    calories: nutritionData.calories,
    protein_g: nutritionData.protein,
    carbs_g: nutritionData.carbs,
    fats_g: nutritionData.fat,
    fiber_g: nutritionData.fiber,
    sugar_g: nutritionData.sugar,
    sodium_mg: nutritionData.sodium,
    cholesterol_mg: nutritionData.cholesterol,
    saturated_fat_g: nutritionData.saturated_fat,
    trans_fat_g: nutritionData.trans_fat,
    vitamin_c_mg: nutritionData.vitamin_c,
    calcium_mg: nutritionData.calcium,
    iron_mg: nutritionData.iron,
    potassium_mg: nutritionData.potassium,
    vitamin_d_mcg: nutritionData.vitamin_d,
    serving_size: nutritionData.serving_size,
    items: nutritionData.items || []
  };
}

// Helper function to analyze image with Gemini
async function analyzeWithGemini(imageBase64, model, apiKey) {
  const prompt = `Analyze this food image and provide detailed nutritional information in JSON format with the following structure:
{
  "name": "food name",
  "calories": number,
  "protein": number,
  "fat": number,
  "carbs": number,
  "fiber": number,
  "sugar": number,
  "sodium": number,
  "cholesterol": number,
  "saturated_fat": number,
  "trans_fat": number,
  "vitamin_c": number,
  "calcium": number,
  "iron": number,
  "potassium": number,
  "vitamin_d": number,
  "serving_size": "description",
  "items": [{"name": "item", "calories": number, "portion": "description"}]
}`;

  const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      contents: [
        {
          parts: [
            { text: prompt },
            {
              inline_data: {
                mime_type: 'image/jpeg',
                data: imageBase64
              }
            }
          ]
        }
      ]
    })
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(`Gemini API error: ${errorData.error?.message || 'Unknown error'}`);
  }

  const data = await response.json();
  const content = data.candidates[0].content.parts[0].text;

  // Extract JSON from the response
  const jsonMatch = content.match(/\{[\s\S]*\}/);
  if (!jsonMatch) {
    throw new Error('Could not extract JSON from Gemini response');
  }

  const nutritionData = JSON.parse(jsonMatch[0]);

  // Format response to match expected format
  return {
    food_name: nutritionData.name,
    calories: nutritionData.calories,
    protein_g: nutritionData.protein,
    carbs_g: nutritionData.carbs,
    fats_g: nutritionData.fat,
    fiber_g: nutritionData.fiber,
    sugar_g: nutritionData.sugar,
    sodium_mg: nutritionData.sodium,
    cholesterol_mg: nutritionData.cholesterol,
    saturated_fat_g: nutritionData.saturated_fat,
    trans_fat_g: nutritionData.trans_fat,
    vitamin_c_mg: nutritionData.vitamin_c,
    calcium_mg: nutritionData.calcium,
    iron_mg: nutritionData.iron,
    potassium_mg: nutritionData.potassium,
    vitamin_d_mcg: nutritionData.vitamin_d,
    serving_size: nutritionData.serving_size,
    items: nutritionData.items || []
  };
}

// Helper function to analyze image with Ollama
async function analyzeWithOllama(imageBase64, model, credentials, progressCallback = null) {
  // Use the Ollama proxy endpoint for image analysis
  const ollamaProxyUrl = process.env.OLLAMA_PROXY_URL || 'http://ollama-proxy:8002';

  console.log('===== OLLAMA ANALYSIS DEBUG =====');
  console.log('Ollama proxy URL:', ollamaProxyUrl);
  console.log('Model:', model);
  console.log('Credentials type:', typeof credentials);
  console.log('Image base64 length:', imageBase64 ? imageBase64.length : 'undefined');
  console.log('Progress callback provided:', !!progressCallback);

  // For Ollama, credentials should be an object with clientId and clientSecret
  // But the analyze endpoint is still passing apiKey, so we need to handle both cases
  let clientId, clientSecret;

  if (typeof credentials === 'string') {
    // Legacy case - apiKey is passed as string, but Ollama needs clientId/clientSecret
    // This suggests the frontend is not properly configured for Ollama
    throw new Error('Ollama requires Client ID and Client Secret, not API Key. Please configure Ollama credentials in settings.');
  } else if (credentials && credentials.clientId && credentials.clientSecret) {
    clientId = credentials.clientId;
    clientSecret = credentials.clientSecret;
  } else {
    throw new Error('Missing Ollama credentials. Please configure Client ID and Client Secret in settings.');
  }

  console.log('Client ID (first 10 chars):', clientId ? clientId.substring(0, 10) + '...' : 'undefined');
  console.log('Client Secret (first 10 chars):', clientSecret ? clientSecret.substring(0, 10) + '...' : 'undefined');

  // Send progress update for initial request
  if (progressCallback) {
    progressCallback({
      stage: 'uploading',
      progress: 10,
      message: 'Sending image to Ollama...'
    });
  }

  const response = await fetch(`${ollamaProxyUrl}/ai/api/image/`, {
    method: 'POST',
    headers: {
      'X-Client-ID': clientId,
      'X-Client-Secret': clientSecret,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      image: imageBase64,
      model: model
    })
  });

  console.log('Response status:', response.status);
  console.log('Response headers:', Object.fromEntries(response.headers.entries()));

  if (!response.ok) {
    let errorData;
    try {
      errorData = await response.json();
    } catch (e) {
      throw new Error(`Ollama API error: HTTP ${response.status} - ${response.statusText}`);
    }
    throw new Error(`Ollama API error: ${errorData.error?.message || errorData.message || `HTTP ${response.status}`}`);
  }

  // Send progress update for processing
  if (progressCallback) {
    progressCallback({
      stage: 'analyzing',
      progress: 90,
      message: 'Processing image with Ollama...'
    });
  }

  const data = await response.json();
  console.log('Ollama response data:', JSON.stringify(data, null, 2));

  // Check if this is the problematic "Grilled Salmon" data
  if (data.name === 'Grilled Salmon' && data.calories === 420) {
    console.error('===== FOUND THE SOURCE OF GRILLED SALMON DATA =====');
    console.error('This data is coming from the Ollama proxy response!');
    console.error('Full response data:', JSON.stringify(data, null, 2));
    console.error('Model used:', model);
    console.error('Ollama proxy URL:', ollamaProxyUrl);
    console.error('This suggests the Ollama model itself is returning this sample data');
  }

  // Send completion progress
  if (progressCallback) {
    progressCallback({
      stage: 'complete',
      progress: 100,
      message: 'Analysis complete!'
    });
  }

  // Check if this is sample data and prevent it from being returned
  const foodName = data.food_name || data.name;
  const calories = data.calories;

  const sampleDataPatterns = [
    { name: 'Grilled Salmon', calories: 420 },
    { name: 'Sample Food' },
    { name: 'Test Food' },
    { name: 'Demo Food' },
    { name: 'Example Meal' },
    { name: 'Placeholder' }
  ];

  const isSampleData = sampleDataPatterns.some(pattern => {
    if (pattern.calories) {
      return foodName === pattern.name && calories === pattern.calories;
    }
    return foodName === pattern.name;
  });

  if (isSampleData) {
    console.error('===== SAMPLE DATA DETECTED IN OLLAMA RESPONSE - BLOCKING =====');
    console.error('Sample data detected:', foodName, calories);
    console.error('Full response data:', JSON.stringify(data, null, 2));
    throw new Error('Sample data detected in analysis response. The AI model returned test/sample data instead of analyzing the actual food image. Please try again with a clearer image.');
  }

  // The Ollama proxy should return data in the expected format already
  // But let's ensure it matches the expected structure
  return {
    food_name: foodName,
    calories: calories,
    protein_g: data.protein_g || data.protein,
    carbs_g: data.carbs_g || data.carbs,
    fats_g: data.fats_g || data.fat,
    fiber_g: data.fiber_g || data.fiber,
    sugar_g: data.sugar_g || data.sugar,
    sodium_mg: data.sodium_mg || data.sodium,
    cholesterol_mg: data.cholesterol_mg || data.cholesterol,
    saturated_fat_g: data.saturated_fat_g || data.saturated_fat,
    trans_fat_g: data.trans_fat_g || data.trans_fat,
    vitamin_c_mg: data.vitamin_c_mg || data.vitamin_c,
    calcium_mg: data.calcium_mg || data.calcium,
    iron_mg: data.iron_mg || data.iron,
    potassium_mg: data.potassium_mg || data.potassium,
    vitamin_d_mcg: data.vitamin_d_mcg || data.vitamin_d,
    serving_size: data.serving_size,
    items: data.items || []
  };
}

module.exports = router;
