#!/usr/bin/env node

/**
 * Database cleanup script to remove sample data
 * This script connects to the database and removes any sample/test data
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function cleanupSampleData() {
  console.log('🧹 Starting sample data cleanup...');
  
  try {
    // Sample food names to remove
    const sampleNames = [
      'Grilled Salmon',
      'Sample Food',
      'Test Food', 
      'Demo Food',
      'Example Meal',
      'Placeholder'
    ];

    // Build the OR conditions for Prisma
    const nameConditions = sampleNames.map(name => ({
      name: {
        contains: name,
        mode: 'insensitive'
      }
    }));

    // Count existing sample data
    const sampleMealsCount = await prisma.meals_logged.count({
      where: {
        OR: nameConditions
      }
    });

    console.log(`📊 Found ${sampleMealsCount} sample meals to remove`);

    if (sampleMealsCount > 0) {
      // Remove sample meals
      const deletedMeals = await prisma.meals_logged.deleteMany({
        where: {
          OR: nameConditions
        }
      });

      console.log(`✅ Removed ${deletedMeals.count} sample meals`);
    } else {
      console.log('✅ No sample meals found to remove');
    }

    // Check if foods table exists and clean it too
    try {
      const sampleFoodsCount = await prisma.foods.count({
        where: {
          OR: nameConditions
        }
      });

      console.log(`📊 Found ${sampleFoodsCount} sample foods to remove`);

      if (sampleFoodsCount > 0) {
        const deletedFoods = await prisma.foods.deleteMany({
          where: {
            OR: nameConditions
          }
        });

        console.log(`✅ Removed ${deletedFoods.count} sample foods`);
      } else {
        console.log('✅ No sample foods found to remove');
      }
    } catch (error) {
      console.log('ℹ️  Foods table not found or accessible, skipping...');
    }

    // Show remaining data count
    const remainingMeals = await prisma.meals_logged.count();
    console.log(`📈 Remaining meals in database: ${remainingMeals}`);

    // Show recent meals to verify cleanup
    const recentMeals = await prisma.meals_logged.findMany({
      select: {
        name: true,
        calories: true,
        timestamp: true
      },
      orderBy: {
        timestamp: 'desc'
      },
      take: 5
    });

    if (recentMeals.length > 0) {
      console.log('\n📋 Recent meals (to verify cleanup):');
      recentMeals.forEach((meal, index) => {
        console.log(`  ${index + 1}. ${meal.name} (${meal.calories} cal) - ${meal.timestamp}`);
      });
    } else {
      console.log('\n📋 No meals found in database');
    }

    console.log('\n🎉 Sample data cleanup completed successfully!');

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the cleanup
if (require.main === module) {
  cleanupSampleData()
    .then(() => {
      console.log('✅ Cleanup script finished');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Cleanup script failed:', error);
      process.exit(1);
    });
}

module.exports = { cleanupSampleData };
