# Calcounta API Documentation

## Base URL

```
http://localhost:86/api
```

## Authentication

All endpoints except for registration and login require authentication via JW<PERSON> token.

Include the token in the Authorization header:

```
Authorization: Bearer <token>
```

## Endpoints

### Authentication

#### Register User

```
POST /auth/register
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword",
  "gender": "male|female|other",
  "dob": "1990-01-01",
  "height_cm": 175,
  "weight_kg": 70,
  "goal_type": "lose|gain|maintain",
  "target_weight": 65,
  "activity_level": "sedentary|light|moderate|active|very_active"
}
```

**Response:**
```json
{
  "id": "uuid",
  "email": "<EMAIL>",
  "token": "jwt_token"
}
```

#### Login

```
POST /auth/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

**Response:**
```json
{
  "id": "uuid",
  "email": "<EMAIL>",
  "token": "jwt_token"
}
```

### User Profile

#### Get User Profile

```
GET /users/profile
```

**Response:**
```json
{
  "id": "uuid",
  "email": "<EMAIL>",
  "gender": "male",
  "dob": "1990-01-01",
  "height_cm": 175,
  "weight_kg": 70,
  "goal_type": "lose",
  "target_weight": 65,
  "activity_level": "moderate",
  "daily_calorie_goal": 2000
}
```

#### Update User Profile

```
PUT /users/profile
```

**Request Body:**
```json
{
  "gender": "male",
  "height_cm": 175,
  "weight_kg": 70,
  "goal_type": "lose",
  "target_weight": 65,
  "activity_level": "moderate"
}
```

**Response:**
```json
{
  "id": "uuid",
  "email": "<EMAIL>",
  "gender": "male",
  "dob": "1990-01-01",
  "height_cm": 175,
  "weight_kg": 70,
  "goal_type": "lose",
  "target_weight": 65,
  "activity_level": "moderate",
  "daily_calorie_goal": 2000
}
```

### Food Analysis

#### Analyze Food Image (Client-side API)

```
POST /analyze/client
```

**Request Body:**
- Form data with the following fields:
  - `image`: The food image file
  - `apiProvider`: "openai" or "gemini"
  - `apiModel`: The model to use (e.g., "gpt-4o", "gemini-1.5-pro")
  - `apiKey`: The API key for the selected provider

**Response:**
```json
{
  "food_name": "Grilled Chicken Salad",
  "calories": 350,
  "protein_g": 30,
  "carbs_g": 15,
  "fats_g": 20,
  "items": [
    {
      "name": "Grilled Chicken",
      "calories": 200,
      "portion": "150g"
    }
  ]
}
```

### Meals

#### Log a Meal

```
POST /meals
```

**Request Body:**
```json
{
  "food_id": "uuid", // Optional, if using a saved food
  "name": "Grilled Chicken Salad", // Required if food_id not provided
  "calories": 350,
  "protein_g": 30,
  "carbs_g": 15,
  "fats_g": 20,
  "serving_size_g": 250,
  "image_url": "optional/path/to/image.jpg",
  "timestamp": "2023-05-12T12:30:00Z" // Optional, defaults to current time
}
```

**Response:**
```json
{
  "id": "uuid",
  "user_id": "uuid",
  "food_id": "uuid",
  "name": "Grilled Chicken Salad",
  "calories": 350,
  "protein_g": 30,
  "carbs_g": 15,
  "fats_g": 20,
  "serving_size_g": 250,
  "image_url": "path/to/image.jpg",
  "timestamp": "2023-05-12T12:30:00Z"
}
```

#### Get Meals for Date Range

```
GET /meals?start_date=2023-05-01&end_date=2023-05-12
```

**Query Parameters:**
- `start_date`: ISO date string (required)
- `end_date`: ISO date string (required)

**Response:**
```json
[
  {
    "id": "uuid",
    "user_id": "uuid",
    "food_id": "uuid",
    "name": "Grilled Chicken Salad",
    "calories": 350,
    "protein_g": 30,
    "carbs_g": 15,
    "fats_g": 20,
    "serving_size_g": 250,
    "image_url": "path/to/image.jpg",
    "timestamp": "2023-05-12T12:30:00Z"
  }
]
```

### Goals

#### Get Current Goal

```
GET /goals/current
```

**Response:**
```json
{
  "id": "uuid",
  "user_id": "uuid",
  "start_date": "2023-05-01",
  "end_date": "2023-07-01",
  "target_change": -5,
  "pace": -0.5,
  "daily_calorie_goal": 2000
}
```

#### Update Goal

```
PUT /goals/current
```

**Request Body:**
```json
{
  "target_change": -5,
  "pace": -0.5
}
```

**Response:**
```json
{
  "id": "uuid",
  "user_id": "uuid",
  "start_date": "2023-05-01",
  "end_date": "2023-07-01",
  "target_change": -5,
  "pace": -0.5,
  "daily_calorie_goal": 2000
}
```

## Error Responses

All endpoints return standard HTTP status codes:

- 200: Success
- 400: Bad Request (invalid input)
- 401: Unauthorized (invalid or missing token)
- 404: Not Found
- 500: Server Error

Error response body:

```json
{
  "error": "Error message description"
}
```
